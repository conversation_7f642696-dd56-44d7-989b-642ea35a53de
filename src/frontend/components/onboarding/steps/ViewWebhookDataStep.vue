<template>
  <div class="space-y-4">
    <!-- Action Card -->
    <div class="card bg-base-100 border border-base-300">
      <div class="card-body p-4">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <div>
            <h4 class="font-medium text-base-content">See your webhook in action!</h4>
            <p class="text-sm text-base-content/70">
              View your email in the logs and see the JSON payload that gets sent to webhooks
            </p>
          </div>
        </div>

        <!-- Recent Logs with JSON Preview -->
        <div v-if="recentLogs.length > 0" class="mt-4 space-y-4">
          <div class="text-sm font-medium text-base-content mb-2">📬 Your recent emails:</div>
          
          <div
            v-for="(log, index) in recentLogs.slice(0, 2)"
            :key="log.id"
            class="border border-base-300 rounded-lg overflow-hidden"
          >
            <!-- Email Summary -->
            <div class="p-3 bg-success/5 border-l-4 border-success">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-base-content truncate">
                    📧 {{ log.subject || 'No subject' }}
                  </div>
                  <div class="text-xs text-base-content/60">
                    From: {{ log.fromAddress || 'Unknown' }} • {{ formatTime(log.createdAt) }}
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="badge badge-success badge-sm">Received</span>
                  <button
                    @click="toggleJsonView(index)"
                    class="btn btn-ghost btn-xs"
                  >
                    {{ showJson[index] ? 'Hide' : 'Show' }} JSON
                  </button>
                </div>
              </div>
            </div>

            <!-- JSON Payload (Collapsible) -->
            <div v-if="showJson[index]" class="bg-base-200">
              <div class="p-3 border-t border-base-300">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-xs font-medium text-base-content/70">🔗 Webhook payload:</span>
                  <button
                    @click="copyJson(log)"
                    class="btn btn-outline btn-xs"
                    :class="{ 'btn-success': copiedIndex === index }"
                  >
                    {{ copiedIndex === index ? 'Copied!' : 'Copy JSON' }}
                  </button>
                </div>
                <div class="mockup-code text-xs text-base-content max-h-48 overflow-y-auto">
                  <pre><code>{{ formatJsonPayload(log) }}</code></pre>
                </div>
              </div>
            </div>
          </div>

          <!-- Success Message -->
          <div class="p-3 bg-primary/10 rounded-lg border border-primary/20">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <div>
                <p class="text-sm text-primary font-medium">Perfect! You can see how webhooks work!</p>
                <p class="text-xs text-primary/80">This JSON data would be sent to your webhook URL via HTTP POST when you set up your own domain.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- No logs state -->
        <div v-else class="mt-4 p-3 bg-info/10 rounded-lg border border-info/20">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p class="text-sm text-info font-medium">Waiting for your email...</p>
              <p class="text-xs text-info/80">Make sure you've sent an email to your test address.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex flex-wrap gap-2 justify-center">
      <button
        @click="refreshLogs"
        class="btn btn-outline btn-sm"
        :disabled="isLoading"
      >
        <div v-if="isLoading" class="loading loading-spinner loading-xs mr-1"></div>
        <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Refresh
      </button>

      <button
        v-if="recentLogs.length > 0"
        @click="markAsCompleted"
        class="btn btn-success btn-sm"
      >
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        I understand webhooks!
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from '@composables/useToast'

interface Props {
  step?: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  complete: []
}>()

// State
const recentLogs = ref<any[]>([])
const isLoading = ref(false)
const pollInterval = ref<number | null>(null)
const showJson = ref<boolean[]>([])
const copiedIndex = ref<number | null>(null)
const hasShownSuccessToast = ref(false)
const router = useRouter()
const { success } = useToast()

// Methods
const loadRecentLogs = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    const response = await fetch('/api/logs?limit=3')
    const data = await response.json()
    
    if (data.logs) {
      recentLogs.value = data.logs
      // Initialize showJson array
      showJson.value = new Array(data.logs.length).fill(false)
      
      // Auto-complete if we have logs and show success toast only once
      if (data.logs.length > 0 && !props.step?.completed && !hasShownSuccessToast.value) {
        hasShownSuccessToast.value = true
        setTimeout(() => {
          success('Perfect! You can see how the webhook system works.')
        }, 1000)

        // Stop polling once we have emails to prevent repeated toasts and JSON collapse
        stopPolling()
      }
    }
  } catch (error) {
    console.error('Failed to load logs:', error)
  } finally {
    isLoading.value = false
  }
}

const toggleJsonView = (index: number) => {
  showJson.value[index] = !showJson.value[index]
}

const copyJson = async (log: any) => {
  const jsonPayload = formatJsonPayload(log)
  const index = recentLogs.value.indexOf(log)
  
  try {
    await navigator.clipboard.writeText(jsonPayload)
    copiedIndex.value = index
    success('JSON payload copied to clipboard!')
    
    setTimeout(() => {
      copiedIndex.value = null
    }, 2000)
  } catch (error) {
    console.error('Failed to copy JSON:', error)
  }
}

const formatJsonPayload = (log: any) => {
  // Use the actual webhook payload if available (for test webhooks)
  if (log.webhookPayload) {
    return JSON.stringify(log.webhookPayload, null, 2)
  }

  // Fallback: Create a realistic webhook payload from log data
  const payload = {
    id: log.id || `msg_${Date.now()}`,
    timestamp: log.createdAt || new Date().toISOString(),
    from: log.fromAddress || '<EMAIL>',
    to: log.toAddresses || ['<EMAIL>'],
    subject: log.subject || 'Test Email',
    body: {
      text: 'Email content here...',
      html: '<p>Email content here...</p>'
    },
    headers: {
      'message-id': log.messageId || `<${Date.now()}@example.com>`,
      'date': log.createdAt || new Date().toISOString()
    },
    attachments: []
  }

  return JSON.stringify(payload, null, 2)
}

const refreshLogs = () => {
  loadRecentLogs()
}

const goToLogs = () => {
  router.push('/logs')
}

const markAsCompleted = () => {
  emit('complete')
}

const formatTime = (timestamp: string) => {
  if (!timestamp) return 'Unknown time'

  const date = new Date(timestamp)
  if (isNaN(date.getTime())) return 'Invalid Date'

  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const startPolling = () => {
  // Load immediately
  loadRecentLogs()
  
  // Then poll every 10 seconds, but only if we haven't found emails yet
  pollInterval.value = window.setInterval(() => {
    if (!hasShownSuccessToast.value) {
      loadRecentLogs()
    } else {
      stopPolling()
    }
  }, 10000)
}

const stopPolling = () => {
  if (pollInterval.value) {
    clearInterval(pollInterval.value)
    pollInterval.value = null
  }
}

// Lifecycle
onMounted(() => {
  startPolling()
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style scoped>
.mockup-code {
  background: var(--fallback-b2, oklch(var(--b2)));
}
</style>
