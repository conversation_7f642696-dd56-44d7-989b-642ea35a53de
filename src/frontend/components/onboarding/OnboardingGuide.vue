<template>
  <div v-if="shouldShowOnboarding" class="space-y-6">
    <!-- Welcome Header -->
    <div class="text-center py-6">
      <h2 class="text-2xl font-bold text-base-content mb-2">
        🎉 Welcome to Mail2Webhook!
      </h2>
      <p class="text-base-content/70 max-w-2xl mx-auto">
        You're 1 step away from your first webhook! Choose how you'd like to get started:
      </p>
    </div>

    <!-- Path Selection (if no path selected) -->
    <div v-if="!selectedPath" class="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
      <div
        v-for="path in paths"
        :key="path.id"
        @click="handlePathSelection(path.id)"
        class="card bg-base-100 border-2 border-base-300 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 min-h-[400px]"
        :class="{ 'border-primary bg-primary/5': path.id === 'instant' }"
      >
        <div class="card-body flex flex-col h-full">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 rounded-lg bg-primary/10">
                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="path.icon" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-base-content">{{ path.title }}</h3>
            </div>
            <div v-if="path.id === 'instant'" class="badge badge-primary badge-sm">Recommended</div>
          </div>

          <p class="text-base-content/70 mb-4">{{ path.description }}</p>

          <!-- Steps Preview -->
          <div class="flex-1 space-y-2 mb-4">
            <p class="text-sm font-medium text-base-content/80 mb-3">Here's what you'll do:</p>
            <div class="space-y-2">
              <div
                v-for="(step, index) in (path.id === 'instant' ? path.steps.slice(0, 3) : [
                  { title: 'Add your domain' },
                  { title: 'Configure DNS records' },
                  { title: 'Create webhooks' },
                  { title: 'Test & verify' }
                ])"
                :key="step.title"
                class="flex items-start space-x-2 text-sm"
              >
                <div
                  class="w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5"
                  :class="index === 0 ? 'bg-primary/20 text-primary' : 'bg-base-300 text-base-content'"
                >
                  {{ index + 1 }}
                </div>
                <span class="text-base-content/70 leading-5">{{ step.title }}</span>
              </div>
            </div>
          </div>

          <div class="card-actions justify-end mt-auto">
            <button class="btn btn-primary btn-sm">
              Get started
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Selected Path Progress -->
    <div v-else class="max-w-4xl mx-auto">
      <!-- Path Header with Switch Option -->
      <div class="flex items-center justify-between mb-6 p-4 bg-base-100 rounded-lg border border-base-300">
        <div class="flex items-center space-x-3">
          <div class="p-2 rounded-lg bg-primary/10">
            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="currentPath?.icon" />
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-base-content">{{ currentPath?.title }}</h3>
            <p class="text-sm text-base-content/70">{{ currentPath?.description }}</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- Progress -->
          <div class="text-sm text-base-content/70">
            {{ overallProgress.completed }}/{{ overallProgress.total }} completed
          </div>
          <div class="radial-progress text-primary" :style="`--value:${overallProgress.percentage}`" role="progressbar">
            {{ overallProgress.percentage }}%
          </div>
        </div>
      </div>

      <!-- Steps for instant path -->
      <div v-if="selectedPath === 'instant'" class="space-y-4">
        <div
          v-for="(step, index) in currentPath?.steps"
          :key="step.id"
          class="card bg-base-100 border border-base-300"
          :class="{
            'border-success bg-success/5': step.completed,
            'border-primary bg-primary/5': !step.completed && isActiveStep(step, index),
            'border-base-300': !step.completed && !isActiveStep(step, index)
          }"
        >
          <div class="card-body py-4">
            <!-- Step Header -->
            <div class="flex items-center space-x-4" :class="{ 'mb-4': !step.completed && isActiveStep(step, index) }">
              <!-- Step Number/Checkmark -->
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center font-medium"
                :class="{
                  'bg-success text-success-content': step.completed,
                  'bg-primary text-primary-content': !step.completed && isActiveStep(step, index),
                  'bg-base-300 text-base-content': !step.completed && !isActiveStep(step, index)
                }"
              >
                <svg v-if="step.completed" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span v-else>{{ index + 1 }}</span>
              </div>

              <!-- Step Content -->
              <div class="flex-1">
                <h4 class="font-medium text-base-content">{{ step.title }}</h4>
                <p class="text-sm text-base-content/70">{{ step.description }}</p>
              </div>
            </div>

            <!-- Step Action (Full Width) -->
            <div v-if="!step.completed && isActiveStep(step, index)">
              <component
                :is="getStepComponent(step.id)"
                :step="step"
                :user-id="userId"
                @complete="completeStep(step.id)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Real setup path should not reach here since it redirects to domains -->

      <!-- Completion Message -->
      <div v-if="overallProgress.percentage === 100" class="text-center py-6">
        <div class="max-w-md mx-auto">
          <div class="text-4xl mb-4">🎉</div>
          <h3 class="text-xl font-semibold text-base-content mb-2">
            Congratulations!
          </h3>
          <p class="text-base-content/70 mb-4">
            Now that you've seen how it works, let's set up your own domain. 
          </p>
          <div class="space-x-2">
            <button
              v-if="selectedPath === 'instant'"
              @click="handlePathSelection('real')"
              class="btn btn-primary"
            >
              Setup your own domain
            </button>
          </div>
        </div>
      </div>

      <!-- Dismiss Option -->
      <div class="text-center mt-6">
        <button
          @click="dismissOnboarding"
          class="btn btn-ghost btn-sm text-base-content/50"
        >
          Skip onboarding
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useOnboarding } from '../../composables/useOnboarding'

interface Props {
  userId?: string
}

const props = withDefaults(defineProps<Props>(), {
  userId: ''
})

// Import step components (only for instant path)
import TestEmailStep from './steps/TestEmailStep.vue'
import SendEmailStep from './steps/SendEmailStep.vue'
import ViewWebhookDataStep from './steps/ViewWebhookDataStep.vue'

const router = useRouter()

const {
  shouldShowOnboarding,
  selectedPath,
  paths,
  currentPath,
  overallProgress,
  selectPath,
  completeStep,
  dismissOnboarding
} = useOnboarding()

// Handle path selection
const handlePathSelection = (pathId: string) => {
  if (pathId === 'real') {
    // For real setup, launch modal directly then redirect after completion
    const tryOpenModal = (attempts = 0) => {
      const maxAttempts = 10

      if ((window as any).openModal) {
        // Dismiss onboarding first
        dismissOnboarding()

        // Open modal with callback to redirect after completion
        ;(window as any).openModal('create-domain', {
          onSuccess: () => {
            // Redirect to domains after successful domain creation
            router.push('/domains')
          },
          onCancel: () => {
            // Redirect to domains even if cancelled
            router.push('/domains')
          }
        })
        return true
      } else if ((window as any).vueModal) {
        dismissOnboarding()
        ;(window as any).vueModal.open('create-domain', {
          onSuccess: () => router.push('/domains'),
          onCancel: () => router.push('/domains')
        })
        return true
      }

      if (attempts < maxAttempts) {
        setTimeout(() => tryOpenModal(attempts + 1), 100)
      } else {
        console.error('Modal system not available, falling back to redirect')
        // Fallback to old behavior if modal system fails
        dismissOnboarding()
        router.push('/domains')
      }
      return false
    }

    tryOpenModal()
  } else {
    // For instant path, use normal selection
    selectPath(pathId as 'instant' | 'real')
  }
}

// Map step IDs to components (only for instant path)
const stepComponents = {
  'copy-test-email': TestEmailStep,
  'send-test-email': SendEmailStep,
  'view-webhook-data': ViewWebhookDataStep
}

const getStepComponent = (stepId: string) => {
  return stepComponents[stepId as keyof typeof stepComponents] || 'div'
}

const isActiveStep = (step: any, index: number) => {
  if (!currentPath.value) return false

  // Find the first incomplete step
  const firstIncompleteIndex = currentPath.value.steps.findIndex(s => !s.completed)

  // This step is active if it's the first incomplete step
  return index === firstIncompleteIndex
}
</script>

<style scoped>
.radial-progress {
  --size: 3rem;
  --thickness: 3px;
}
</style>
